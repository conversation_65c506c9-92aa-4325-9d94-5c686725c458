
services:
  backend:
    container_name: bilinote-backend
    build:
      context: .
      dockerfile: backend/Dockerfile.gpu
    env_file:
      - .env
    environment:
      - BACKEND_PORT=${BACKEND_PORT}
      - BACKEND_HOST=${BACKEND_HOST}
    volumes:
      - ./backend:/app
    expose:
      - "${BACKEND_PORT}"  # 不再对外暴露，用于 nginx 内部通信
    deploy:
      resources:
        reservations:
          devices:
            - driver: "nvidia"
              count: "all"
              capabilities: ["gpu"]

  frontend:
    container_name: bilinote-frontend
    build:
      context: .
      dockerfile: BillNote_frontend/Dockerfile
    env_file:
      - .env
    expose:
      - "80"  # 不暴露给宿主机，只供 nginx 访问

  nginx:
    container_name: bilinote-nginx
    image: nginx:1.25-alpine
    ports:
      - "${APP_PORT}:80"
    volumes:
      - ./nginx/default.conf:/etc/nginx/conf.d/default.conf
    depends_on:
      - backend
      - frontend
