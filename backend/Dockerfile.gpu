FROM nvidia/cuda:12.6.0-cudnn-runtime-ubuntu24.04

RUN apt update && \
    apt install -y ffmpeg python3-pip && \
    apt clean all

# 设置 Hugging Face 镜像源环境变量
ENV HF_ENDPOINT=https://hf-mirror.com

WORKDIR /app
COPY ./backend /app
RUN pip install --no-cache-dir -i https://pypi.mirrors.ustc.edu.cn/simple -r requirements.txt --break-system-packages
RUN pip install --no-cache-dir -i https://pypi.mirrors.ustc.edu.cn/simple transformers[torch]>=4.23 --break-system-packages

CMD ["python3", "main.py"]
