[{"id": "openai", "name": "OpenAI", "type": "built-in", "logo": "OpenAI", "api_key": "", "base_url": "https://api.openai.com/v1"}, {"id": "deepseek", "name": "DeepSeek", "type": "built-in", "logo": "DeepSeek", "api_key": "", "base_url": "https://api.deepseek.com"}, {"id": "qwen", "name": "<PERSON><PERSON>", "type": "built-in", "logo": "<PERSON><PERSON>", "api_key": "", "base_url": "https://dashscope.aliyuncs.com/compatible-mode/v1"}, {"id": "<PERSON>", "name": "<PERSON>", "type": "built-in", "logo": "<PERSON>", "api_key": "", "base_url": "https://"}, {"id": "gemini", "name": "Gemini", "type": "built-in", "logo": "Gemini", "api_key": "", "base_url": "https://generativelanguage.googleapis.com/v1beta/openai/"}, {"id": "groq", "name": "Groq", "type": "built-in", "logo": "Groq", "api_key": "", "base_url": "https://api.groq.com/openai/v1"}, {"id": "ollama", "name": "ollama", "type": "built-in", "logo": "Ollama", "api_key": "", "base_url": "http://127.0.0.1:11434/v1"}]